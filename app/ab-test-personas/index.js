'use strict';

define(['simple!core/template', 'simple!core/widgets', 'simple!core/plugin', 'simple!core/store', 'simple!core/table'
], function(Template,  Widgets, Plugin, Store, Table) {
    function loadPersonas(id) {
        return new Promise(resolve => {
            Store([`swallow!ab-test/data/view`]).load([{
                aliases: 'result',
                params: {
                    id
                }
            }]).then(data => {
                resolve(data.data.result)
            })
        })
    }
    var editPersonas = async function (table, lineData) {
        const res = await loadPersonas(lineData.id)
        const viewData = res.data;
        var personasConditions = JSON.parse(res.data.personasConditions||'{}')
        var config = {
            title: '用户画像',
            width: 500,
            store: 'swallow!ab-test/data/update',
            success: function (obj, dialog) {
                dialog.close()
                table.render()
            },
            form: {
                submitHandler: function (form) {
                    var bindJiaxiao = $(form).item('bindJiaxiao').val();
                    var commentJiaxiao = $(form).item('commentJiaxiao').val();
                    var examDistanceDays = $(form).item('examDistanceDays').val();
                    examDistanceDays = JSON.parse(examDistanceDays || '{}')
                    var answeredQuestions = $(form).item('answeredQuestions').val();
                    answeredQuestions = JSON.parse(answeredQuestions || '{}')
                    var correctRate = $(form).item('correctRate').val();
                    correctRate = JSON.parse(correctRate || '{}')
                    var lastExamScore = $(form).item('lastExamScore').val();
                    lastExamScore = JSON.parse(lastExamScore || '{}')
                    var examTimes = $(form).item('examTimes').val();
                    examTimes = JSON.parse(examTimes || '{}')
                    var paymentAttempts = $(form).item('paymentAttempts').val();
                    paymentAttempts = JSON.parse(paymentAttempts || '{}')
                    var passTimes = $(form).item('passTimes').val();
                    var userGroupJ = $(form).item('userGroupJ').val();
                    var userGroupJy = $(form).item('userGroupJy').val();
                    passTimes = JSON.parse(passTimes || '{}');
                    var ageGroup = $(form).item('ageGroup').filter(':checked').map(function () { return this.value}).get().join(',');
                    var educationLevel = $(form).item('educationLevel').filter(':checked').map(function () { return this.value}).get().join(',');
                    let so = [{
                        name: '距离预约考试天数',
                        value: examDistanceDays,
                    },{
                        name: '已做题数量',
                        value: answeredQuestions,
                    },{
                        name: '做题正确率',
                        value: correctRate,
                    },{
                        name: '最近1次考试成绩',
                        value: lastExamScore,
                    },{
                        name: '考试次数',
                        value: examTimes,
                    },{
                        name: '合格次数',
                        value: passTimes,
                    },{
                        name: '去支付次数',
                        value: paymentAttempts,
                    }]
                    for (let ke of so) {
                        let {name, value} = ke
    
                        if ((value.from && value.from % 1 != 0) || (value.to && value.to % 1 != 0)) {
                            Widgets.dialog.alert(`${name}: 请填入大于0的整数`);
                            return;
                        }
    
                        if (name === '做题正确率') {
                            if ((value.from  && (value.from > 100 || value.from < 0)) || (value.to && (value.to > 100 || value.to < 0))) {
                                Widgets.dialog.alert(`${name}: 数值范围0-100`);
                                return;
                            }
                        }
    
                        if (value.from && value.to && +value.from >= +value.to) {
                            Widgets.dialog.alert(`${name}: 左边值需小于右边值`);
                            return;
                        }
                    }
    
                    const value = JSON.stringify({
                        bindJiaxiao,
                        commentJiaxiao,
                        examDistanceDays,
                        answeredQuestions,
                        correctRate,
                        lastExamScore,
                        examTimes,
                        passTimes,
                        paymentAttempts,
                        userGroupJ,
                        userGroupJy,
                        ageGroup: ageGroup.trim(),
                        educationLevel: educationLevel.trim(),
                    })
                    return {
                        ...viewData,
                        personasConditions: value
                    }
                },
            },
            renderAfter: function (config, dom, data) {
            },
            columns: [
                {
                    header: '距离预约考试天数：',
                    dataIndex: 'examDistanceDays',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'examDistanceDays',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '已做题数量：',
                    dataIndex: 'answeredQuestions',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'answeredQuestions',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '做题正确率：',
                    dataIndex: 'correctRate',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'correctRate',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '最近1次考试成绩：',
                    dataIndex: 'lastExamScore',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'lastExamScore',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '考试次数：',
                    dataIndex: 'examTimes',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'examTimes',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '合格次数：',
                    dataIndex: 'passTimes',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'passTimes',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '去支付次数：',
                    dataIndex: 'paymentAttempts',
                    xtype: Plugin('swallow!section2', {
                        dataIndex: 'paymentAttempts',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '绑定驾校：',
                    dataIndex: 'bindJiaxiao',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择'
                        }, {
                            key: 1,
                            value: '已绑定'
                        }, {
                            key: 0,
                            value: '未绑定'
                        }
                    ]
                },
                {
                    header: '评价驾校：',
                    dataIndex: 'commentJiaxiao',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择'
                    },{
                        key:1,
                        value:'已评价'
                    }, {
                        key: 0,
                        value: '未评价'
                    }]
                },
                {
                    header: '符合J标签的用户群体：',
                    dataIndex: 'userGroupJ',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择符合J标签的用户群体'
                        }, {
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '符合Jy标签的用户群体：',
                    dataIndex: 'userGroupJy',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择符合Jy标签的用户群体'
                        }, {
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '年龄：',
                    dataIndex: 'ageGroup',
                    xtype: 'checkbox',
                    store: [
                        {
                            key: "18-24",
                            value: "18-24岁"
                        },
                        {
                            key: "25-30",
                            value: "25-30岁"
                        },
                        {
                            key: "31-40",
                            value: "31-40岁"
                        },
                        {
                            key: "41-50",
                            value: "41-50岁"
                        },
                        {
                            key: "51-100",
                            value: "50岁以上"
                        },
                        {
                            key: "unknown",
                            value: "保密"
                        }
                    ],
                    placeholder: '年龄'
                },
                {
                    header: '学历：',
                    dataIndex: 'educationLevel',
                    xtype: 'checkbox',
                    store: [
                        {
                            key: "university",
                            value: "本科及以上"
                        },
                        {
                            key: "junior_college",
                            value: "大专"
                        },
                        {
                            key: "high_school",
                            value: "高中/中专"
                        },
                        {
                            key: "middle_school",
                            value: "初中"
                        },
                        {
                            key: "primary",
                            value: "小学及以下"
                        },
                        {
                            key: "unknown",
                            value: "保密"
                        }
                    ],
                    placeholder: '学历'
                },
            ],
        }
    
        Table().edit(personasConditions, config)
    }

    return {
        editPersonas: editPersonas
    }
});




