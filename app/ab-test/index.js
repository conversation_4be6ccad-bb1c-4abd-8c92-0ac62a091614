/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!app/comm/tiku', 'swallow!app/ab-test-personas/index'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, TIKU,TPersonas) {
    var tikuArr = [];
    var zimu = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'];

    for (var k in TIKU) {
        tikuArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'swallow!ab-test/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var value = form.rateDistributionCopy.value;
                    var parseValue = JSON.parse(value);
                    var ratios = 0;
                    var names = [];
                    parseValue.forEach(function (item) {
                        ratios += +item.ratio
                        names.push(item.name)
                    })

                    var isSomeName = parseValue.every((item) => item.name !== '');

                    const isNormalizeName =  parseValue.every((item) => zimu.includes(item.name))
                    if (
                        !isNormalizeName
                    ) {
                        Widgets.dialog.alert("分组名称不符合规范！");
                        return
                    }
                    if (names.length !== new Set(names).size) {
                        Widgets.dialog.alert("请删除重复的分组名称！");
                        return
                    }

                    if (!isSomeName) {
                        Widgets.dialog.alert("请填写分组名称！");
                        return
                    }
                    if (ratios != 100) {
                        Widgets.dialog.alert("分组比率总和请等于100！");
                        return
                    }
                    return {
                        rateDistribution: value
                    }
                }
            },
            columns: [
                {
                    header: '策略名称：',
                    dataIndex: 'testKeyName',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: 'key名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'description',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '描述'
                },
                {
                    header: '策略唯一标示：',
                    dataIndex: 'testKey',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '策略唯一标示'
                },

                {
                    header: '车型，多个车型用,分隔：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: tikuArr,
                    placeholder: '车型，多个车型用,分隔'
                },
                {
                    header: '优先级，ID越大优先级越高：',
                    dataIndex: 'priority',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '优先级，ID越大优先级越高'
                },
                {
                    header: '投放开始时间：',
                    dataIndex: 'startTime',
                    xtype: 'date',
                    check: 'required',
                    placeholder: '投放开始时间'
                },
                {
                    header: '投放结束时间：',
                    dataIndex: 'endTime',
                    xtype: 'date',
                    check: 'required',
                    placeholder: '投放结束时间'
                },
                {
                    header: '配置分组：',
                    dataIndex: 'rateDistribution',
                    xtype: Plugin('swallow!group', {
                        dataIndex: 'rateDistribution'
                    }, function (plugin, value) {

                    })
                },
                {
                    header: ' 上线状态',
                    dataIndex: 'onlineStatus',
                    xtype: 'select',
                    store: [{
                        key: 1,
                        value: '待发布'
                    },
                    {
                        key: 2,
                        value: '已发布'
                    }, {
                        key: 3,
                        value: '已下线'
                    }],
                    placeholder: ' 上线状态'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        var searchTikuArr = tikuArr.slice();
        searchTikuArr.unshift({ key: '', value: '选择车型' });
        Table({
            description: 'ab-test列表',
            title: 'ab-test列表',
            search: [
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: searchTikuArr,
                    placeholder: '车型'
                },
                {
                    dataIndex: 'testKeyName',
                    xtype: 'text',
                    placeholder: '策略名称'
                },
                {
                    dataIndex: 'testKey',
                    xtype: 'text',
                    placeholder: '策略key'
                },
                {
                    dataIndex: 'onlineStatus',
                    xtype: 'select',
                    store: [{
                        key: '', value: '选择状态'
                    }, {
                        key: 1,
                        value: '待发布'
                    },
                    {
                        key: 2,
                        value: '已发布'
                    }, {
                        key: 3,
                        value: '已下线'
                    }],
                    placeholder: '上线状态'
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!ab-test/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id',
                            width: 20
                        },
                        {
                            header: '策略名称',
                            dataIndex: 'testKeyName'
                        },
                        {
                            header: '策略描述',
                            dataIndex: 'description'
                        },
                        {
                            header: '策略唯一标示',
                            dataIndex: 'testKey'
                        },
                        {
                            header: '车型',
                            dataIndex: 'carType'
                        },
                        {
                            header: '投放策略',
                            dataIndex: 'conditions'
                        },
                        {
                            header: '投放时间',
                            render: function (data, a, lineData) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss') + '-' + Utils.format.date(lineData.endTime, 'yyyy-MM-dd HH:mm:ss')
                            },
                            dataIndex: 'startTime'
                        },
                        {
                            header: ' 上线状态',
                            dataIndex: 'onlineStatus',
                            render: function (data) {
                                return data == 1 ? '待发布' : data == 2 ? '已发布' : data == 3 ? '已下线' : ''
                            }
                        },
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form, fun) {
                            var value = form.rateDistributionCopy.value;
                            var parseValue = JSON.parse(value);
                            var ratios = 0;
                            var names = [];
                            parseValue.forEach(function (item) {
                                ratios += +item.ratio;
                                names.push(item.name)
                            })
                            const isNormalizeName = parseValue.every((item) => zimu.includes(item.name))
                            if (
                                !isNormalizeName
                            ) {
                                Widgets.dialog.alert("分组名称不符合规范！");
                                return
                            }

                            if (names.length !== new Set(names).size) {
                                Widgets.dialog.alert("请删除重复的分组名称！");
                                return
                            }



                            var isSomeName = parseValue.some((item) => item.name !== '');
                            if (!isSomeName) {
                                Widgets.dialog.alert("请填写分组名称！");
                                return
                            }
                            if (ratios != 100) {
                                Widgets.dialog.alert("分组比率总和请等于100！");
                                return
                            }
                            return {
                                rateDistribution: value
                            }
                        }
                    },
                    store: {
                        load: 'swallow!ab-test/data/view',
                        save: 'swallow!ab-test/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '策略名称：',
                            dataIndex: 'testKeyName',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'key名称'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '描述'
                        },
                        {
                            header: '策略唯一标示：',
                            dataIndex: 'testKey',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            disable: true,
                            placeholder: '策略唯一标示'
                        },

                        {
                            header: '车型，多个车型用,分隔：',
                            dataIndex: 'carType',
                            xtype: 'checkbox',
                            store: tikuArr,
                            placeholder: '车型，多个车型用,分隔'
                        },
                        {
                            header: '优先级，ID越大优先级越高：',
                            dataIndex: 'priority',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '优先级，ID越大优先级越高'
                        },
                        {
                            header: '投放开始时间：',
                            dataIndex: 'startTime',
                            xtype: 'date',
                            check: 'required',
                            render: function (data) {
                                if (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                }
                            },
                            placeholder: '投放开始时间'
                        },
                        {
                            header: '投放结束时间：',
                            dataIndex: 'endTime',
                            xtype: 'date',
                            render: function (data) {
                                if (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                }
                            },
                            check: 'required',
                            placeholder: '投放结束时间'
                        },
                        {
                            header: '配置分组：',
                            dataIndex: 'rateDistribution',
                            xtype: Plugin('swallow!group', {
                                dataIndex: 'rateDistribution',
                                disabled: 'disabled'
                            }, function (plugin, value) {

                            })
                        },
                        {
                            header: ' 上线状态',
                            dataIndex: 'onlineStatus',
                            xtype: 'select',
                            store: [{
                                key: 1,
                                value: '待发布'
                            },
                            {
                                key: 2,
                                value: '已发布'
                            }, {
                                key: 3,
                                value: '已下线'
                            }],
                            placeholder: ' 上线状态'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!ab-test/data/delete'
                },
                {
                    // name: '上架',
                    class: 'info',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认你的操作码？', function (ev, status) {
                            let onlineStatus = lineData.onlineStatus;

                            let target = onlineStatus + 1 == 4 ? 2 : onlineStatus + 1;

                            if (status) {
                                Store(['swallow!ab-test/data/update?onlineStatus=' + target + '&id=' + lineData.id]).save().done(function () {
                                    table.render()
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');

                                    }
                                })

                            }
                        })

                    },
                    render: function (name, arr, index) {
                        let onlineStatus = arr[index].onlineStatus;
                        if (onlineStatus == 1) {
                            return '上架'
                        } else if (onlineStatus == 2) {
                            return '下架';
                        } else if (onlineStatus == 3) {
                            return '上架';
                        }
                    }
                },
                {
                    name: '投放策略',
                    class: 'success',
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Plugin('simple!product-filter', {
                            store: 'swallow!ab-test/data/filter?id=' + lineData.id
                        }).render().done(function () {
                            table.render();
                        });
                    }
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        TPersonas.editPersonas(table, lineData)
                    },
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '策略名称',
                    dataIndex: 'testKeyName'
                },
                {
                    header: '策略描述',
                    dataIndex: 'description'
                },
                {
                    header: '策略唯一标示',
                    dataIndex: 'testKey'
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        var arr = data.split(',');
                        var str = [];
                        arr.forEach(item => {
                            str.push(TIKU[item])
                        })
                        return str.toString();
                    }
                },
                {
                    header: '投放策略',
                    dataIndex: 'conditions',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('投放策略', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.conditions + '</div><br/>')
                        })
                    }
                },
                {
                    header: '投放时间',
                    render: function (data, a, lineData) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss') + '-' + Utils.format.date(lineData.endTime, 'yyyy-MM-dd HH:mm:ss')
                    },
                    dataIndex: 'startTime'
                },
                {
                    header: '分组',
                    dataIndex: 'rateDistribution',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('比例配置json', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.rateDistribution + '</div><br/>')
                        })
                    }
                },

                {
                    header: ' 上线状态',
                    dataIndex: 'onlineStatus',
                    render: function (data) {
                        return data == 1 ? '待发布' : data == 2 ? '已发布' : data == 3 ? '已下线' : ''
                    }
                }
            ]
        }, ['swallow!ab-test/data/list'], panel, function(dom, config, item, table){
            dom.item('title').append($('<strong style="padding-left: 20px; font-size: 16px;color: red;">key请不要使用下划线</strong>'))
        }).render();
    }

    return {
        list: list
    }

});