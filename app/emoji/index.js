/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'swallow!emoji/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: 'emoji表情代码：',
                    dataIndex: 'emoji',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: 'emoji表情代码'
                },
                {
                    header: '文案：',
                    dataIndex: 'description',
                    xtype: 'text',
                    maxlength: 1024,
                    check: 'required',
                    placeholder: '文案'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'emoji列表',
            title: 'emoji列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!emoji/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: 'emoji标签：',
                            dataIndex: 'emoji'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description'
                        },
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'swallow!emoji/data/view',
                        save: 'swallow!emoji/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: 'emoji表情代码：',
                            dataIndex: 'emoji',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'emoji表情代码'
                        },
                        {
                            header: '文案：',
                            dataIndex: 'description',
                            xtype: 'text',
                            maxlength: 1024,
                            check: 'required',
                            placeholder: '文案'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!emoji/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'emoji标签',
                    dataIndex: 'emoji'
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
            ]
        }, ['swallow!emoji/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});