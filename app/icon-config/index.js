/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'swallow!icon-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: ' 类型',
                    dataIndex: 'iconType',
                    xtype: 'select',
                    store: [{
                        key: 1,
                        value: '头部icon'
                    }, {
                        key: 2,
                        value: '技巧icon'
                    }],
                    check: 'required',
                    placeholder: ' 类型 1 头部icon 2 技巧icon'
                },
                {
                    header: '排序：',
                    dataIndex: 'displayOrder',
                    xtype: 'text',
                    check: 'required',
                    placeholder: ' 序号'
                },
                {
                    header: ' 图标地址：',
                    dataIndex: 'icon',
                    xtype: Plugin('swallow!upload', {
                        dataIndex: 'icon',
                        uploadIndex: 'icon',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload2://admin-upload.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: ' 标题：',
                    dataIndex: 'iconName',
                    xtype: 'text',
                    maxlength: 16,
                    check: 'required',
                    placeholder: ' 标题'
                },

                {
                    header: ' 跳转地址：',
                    dataIndex: 'url',
                    xtype: 'textarea',
                    maxlength: 512,
                    check: 'required',
                    placeholder: ' 跳转地址'
                },
                {
                    header: ' 上线状态：',
                    dataIndex: 'onlineStatus',
                    xtype: 'select',
                    store: [{
                        key: 1,
                        value: '上线'
                    }, { key: 2, value: '下线' }],
                    placeholder: ' 上线状态'
                },
                {
                    header: ' 车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择车型'
                    }, {
                        key: 'car',
                        value: '小车'
                    },
                    {
                        key: 'bus',
                        value: '客车'
                    },
                    {
                        key: 'truck',
                        value: '货车'
                    },
                    {
                        key: 'moto',
                        value: '摩托车'
                    },
                    {
                        key: 'light_trailer',
                        value: '轻型牵引挂车'
                    }
                    ]
                },
                {
                    header: ' 科目：',
                    dataIndex: 'kemu',
                    xtype: 'text',
                    check: 'required',
                    placeholder: ' 科目'
                },
                // {
                //     header: 'deleted：',
                //     dataIndex: 'deleted',
                //     xtype: 'radio',
                //     store: [
                //         {
                //             key: true,
                //             value: '是'
                //         },
                //         {
                //             key: false,
                //             value: '否'
                //         }
                //     ]
                // }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'icon-config列表',
            title: 'icon-config列表',
            search: [{
                dataIndex: 'carType',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择车型'
                }, {
                    key: 'car',
                    value: '小车'
                },
                {
                    key: 'bus',
                    value: '客车'
                },
                {
                    key: 'truck',
                    value: '货车'
                },
                {
                    key: 'moto',
                    value: '摩托车'
                },
                {
                    key: 'light_trailer',
                    value: '轻型牵引挂车'
                }
                ]
            }, {
                dataIndex: 'onlineStatus',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择状态'
                }, {
                    key: 1,
                    value: '上线'
                }, {
                    key: 2,
                    value: '下线'
                }]
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!icon-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: ' 类型 1 头部icon 2 技巧icon：',
                            dataIndex: 'iconType'
                        },
                        {
                            header: ' 车型：',
                            dataIndex: 'carType'
                        },
                        {
                            header: ' 科目：',
                            dataIndex: 'kemu'
                        },
                        {
                            header: ' 图标地址：',
                            dataIndex: 'icon'
                        },
                        {
                            header: ' 名称：',
                            dataIndex: 'iconName'
                        },
                        {
                            header: ' 跳转地址：',
                            dataIndex: 'url'
                        },
                        {
                            header: ' 序号：',
                            dataIndex: 'displayOrder'
                        },
                        {
                            header: ' 上线状态，1 上线 2 下线：',
                            dataIndex: 'onlineStatus'
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'swallow!icon-config/data/view',
                        save: 'swallow!icon-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: ' 类型',
                            dataIndex: 'iconType',
                            xtype: 'select',
                            store: [{
                                key: 1,
                                value: '头部icon'
                            }, {
                                key: 2,
                                value: '技巧icon'
                            }],
                            check: 'required',
                            placeholder: ' 类型 1 头部icon 2 技巧icon'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            check: 'required',
                            placeholder: ' 序号'
                        },
                        {
                            header: ' 图标地址：',
                            dataIndex: 'icon',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'icon',
                                uploadIndex: 'icon',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: ' 标题：',
                            dataIndex: 'iconName',
                            xtype: 'text',
                            maxlength: 16,
                            check: 'required',
                            placeholder: ' 标题'
                        },
                        {
                            header: ' 科目：',
                            dataIndex: 'kemu',
                            xtype: 'text',
                            maxlength: 16,
                            check: 'required',
                            placeholder: ' 科目'
                        },

                        {
                            header: ' 跳转地址：',
                            dataIndex: 'url',
                            xtype: 'textarea',
                            maxlength: 512,
                            check: 'required',
                            placeholder: ' 跳转地址'
                        },
                        {
                            header: ' 上线状态：',
                            dataIndex: 'onlineStatus',
                            xtype: 'select',
                            store: [{
                                key: 1,
                                value: '上线'
                            }, { key: 2, value: '下线' }],
                            placeholder: ' 上线状态'
                        },
                        {
                            header: ' 车型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '请选择车型'
                            }, {
                                key: 'car',
                                value: '小车'
                            },
                            {
                                key: 'bus',
                                value: '客车'
                            },
                            {
                                key: 'truck',
                                value: '货车'
                            },
                            {
                                key: 'moto',
                                value: '摩托车'
                            },                    
                            {
                                key: 'light_trailer',
                                value: '轻型牵引挂车'
                            }
                            ]
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!icon-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: ' 类型',
                    dataIndex: 'iconType',
                    render: function (data) {
                        return data == 1 ? '头部Icon' : '技巧icon'
                    }
                },
                {
                    header: ' 科目',
                    dataIndex: 'kemu'
                },
                {
                    header: ' 车型',
                    dataIndex: 'carType'
                },
                {
                    header: '排序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: ' 图标地址',
                    dataIndex: 'icon'
                },
                {
                    header: '名称',
                    dataIndex: 'iconName'
                },
                {
                    header: ' 跳转地址',
                    dataIndex: 'url',
                    render: function (data) {
                        if (data) {

                            return `<a>点击查看</a>`
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('跳转地址', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.url + '</div><br/>')
                        })
                    }
                },

                {
                    header: ' 上线状态',
                    dataIndex: 'onlineStatus',
                    render: function (data) {
                        return data == 1 ? '上线' : '下线'
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['swallow!icon-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
