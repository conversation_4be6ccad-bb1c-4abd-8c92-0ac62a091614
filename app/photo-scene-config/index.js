/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var addOrEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'swallow!photo-scene-config/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '编码：',
                    dataIndex: 'code',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '编码'
                },
                {
                    header: '场景名称：',
                    dataIndex: 'sceneName',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '场景名称'
                },
                {
                    header: '单次识别图片数限制：',
                    dataIndex: 'perImageLimit',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '单次识别图片数限制'
                },
                {
                    header: '拍照教程图片URL：',
                    dataIndex: 'courseImageUrl',
                    xtype: Plugin('swallow!upload', {
                        dataIndex: 'courseImageUrl',
                        uploadIndex: 'courseImageUrl',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload2://admin-upload.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    placeholder: '拍照教程图片URL'
                },
                {
                    header: '拍照教程标题：',
                    dataIndex: 'courseTitle',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '拍照教程标题'
                },
                {
                    header: '拍照教程文案：',
                    dataIndex: 'courseText',
                    xtype: 'textarea',
                    rows: 5,
                    cols: 6,
                    check: 'required',
                    placeholder: '拍照教程文案'
                },
                {
                    header: '指引蒙层：',
                    dataIndex: 'guideMaskUrl',
                    xtype: Plugin('swallow!upload', {
                        dataIndex: 'guideMaskUrl',
                        uploadIndex: 'guideMaskUrl',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload2://admin-upload.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    placeholder: '指引蒙层'
                },
                {
                    header: '指引标题：',
                    dataIndex: 'guideTitle',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '指引标题'
                },
                {
                    header: '指引副标题：',
                    dataIndex: 'guideDescribe',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '指引副标题'
                },
                {
                    header: '是否支持框选：',
                    dataIndex: 'supportFrame',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '是否预判断文字：',
                    dataIndex: 'checkText',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
               
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config)
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '拍照识别场景配置',
            title: '拍照识别场景配置',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addOrEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'code',
                    xtype: 'text',
                    placeholder: '请输入编码以搜索'
                }
            ],
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!photo-scene-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '编码：',
                            dataIndex: 'code'
                        },
                        {
                            header: '场景名称：',
                            dataIndex: 'sceneName'
                        },
                        {
                            header: '单次识别图片数限制：',
                            dataIndex: 'perImageLimit'
                        },
                        {
                            header: 'courseImageUrl：',
                            dataIndex: 'courseImageUrl'
                        },
                        {
                            header: '拍照教程标题：',
                            dataIndex: 'courseTitle'
                        },
                        {
                            header: '拍照教程文案：',
                            dataIndex: 'courseText'
                        },
                        {
                            header: '指引蒙层：',
                            dataIndex: 'guideMaskUrl'
                        },
                        {
                            header: '指引标题：',
                            dataIndex: 'guideTitle'
                        },
                        {
                            header: '指引副标题：',
                            dataIndex: 'guideDescribe'
                        },
                        {
                            header: '是否支持框选：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'supportFrame'
                        },
                        {
                            header: '是否预判断文字：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'checkText'
                        },
                        
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        }

                    ]
                },
                {
                    name: '编辑',
                    class: 'warning',
                    click: function (table, dom, linedata) {
                        addOrEdit(table, linedata)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!photo-scene-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '编码',
                    dataIndex: 'code'
                },
                {
                    header: '场景名称',
                    dataIndex: 'sceneName'
                },
                {
                    header: '单次识别图片数限制',
                    dataIndex: 'perImageLimit'
                },
                {
                    header: '拍照教程图片URL',
                    dataIndex: 'courseImageUrl',
                    render: function (data) {
                        return `<a><img style="height:50px" src="${data}"/><a>`
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html('拍照教程图片URL', {}).done(function (dialog) {
                            $(dialog.body).html(`<img style="width:auto;height:auto" src="${lineData.courseImageUrl}">`)
                        })
                    }
                },
                {
                    header: '拍照教程标题',
                    dataIndex: 'courseTitle'
                },
                {
                    header: '拍照教程文案',
                    dataIndex: 'courseText'
                },
                {
                    header: '指引蒙层',
                    dataIndex: 'guideMaskUrl',
                    render: function (data) {
                        return `<a><img style="height:50px" src="${data}"/><a>`
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html('指引蒙层', {}).done(function (dialog) {
                            $(dialog.body).html(`<img style="width:auto;height:auto" src="${lineData.guideMaskUrl}">`)
                        })
                    }
                },
                {
                    header: '指引标题',
                    dataIndex: 'guideTitle'
                },
                {
                    header: '指引副标题',
                    dataIndex: 'guideDescribe'
                },
                {
                    header: '是否支持框选',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'supportFrame'
                },
                {
                    header: '是否预判断文字',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'checkText'
                },
                
            ]
        }, ['swallow!photo-scene-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});