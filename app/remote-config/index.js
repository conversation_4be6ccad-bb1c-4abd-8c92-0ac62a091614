/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'swallow!plugin/select-district/district3',
    'swallow!plugin/select-district/district2',
    'swallow!app/comm/tiku',
    'swallow!app/edit-personas/index',
    'swallow!resources/lib/codemirror/codemirror',
    'simple_css!resources/lib/codemirror/codemirror'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, District3, District2, TIKU, EdPersonas) {
    var zxCitys = ['120000', '110000', '500000', '310000']
    var getAllCity = function () {
        var list = District3.list;
        var city = [];

        for (var i = 0; i < list.length; ++i) {
            if (list[i].cities && list[i].code != zxCitys[0] && list[i].code != zxCitys[1] && list[i].code != zxCitys[2] && list[i].code != zxCitys[3]) {
                var data = list[i].cities;
                city.push({
                    key: list[i].code,
                    value: list[i].name,
                    search: list[i].name
                })
                for (var j = 0; j < data.length; ++j) {
                    city.push({
                        key: data[j].code,
                        value: data[j].name,
                        search: data[j].name
                    })
                }

            }

        }

        city.unshift({
            key: "310000",
            value: "上海",
            search: '上海'
        })
        city.unshift({
            key: "120000",
            value: "天津",
            search: '天津'
        })
        city.unshift({
            key: "500000",
            value: "重庆",
            search: '重庆'
        })
        city.unshift({
            key: "110000",
            value: "北京",
            search: '北京'
        })
        city.unshift({
            key: "000000",
            value: "全国",
            search: '全国'
        })
        return city;

    }
    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    function isJson(str) {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    }

    var isJsonValue = null;

    var kemuArr = [
        {
            key: 'kemu1',
            value: '科目一'
        },
        {
            key: 'kemu2',
            value: '科目二'
        },
        {
            key: 'kemu3',
            value: '科目三'
        },
        {
            key: 'kemu4',
            value: '科目四'
        },
        {
            key: 'zigezheng',
            value: '资格证'
        }
    ]
    var kemuMap = {}
    kemuArr.forEach(item => {
        kemuMap[item.key] = item.value
    })

    var typeCodeMap = {
        '0': '驾考',
    }


    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'swallow!remote-config/data/insert?typeCode=0',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var patternCodeValue = []
                    $(form).item('patternCodeT')[0].checked && patternCodeValue.push($(form).item('patternCodeT')[0].value);
                    $(form).item('patternCodeT')[1].checked && patternCodeValue.push($(form).item('patternCodeT')[1].value);

                    var sceneCodeTValue = [];
                    $(form).item('sceneCodeT')[0].checked && sceneCodeTValue.push($(form).item('sceneCodeT')[0].value);
                    $(form).item('sceneCodeT')[1].checked && sceneCodeTValue.push($(form).item('sceneCodeT')[1].value);


                    const planKey = $(form).item('planKey').val()
                    const planValue = $(form).item('planValue').val()

                    const plan = (planKey || planValue)  ? JSON.stringify({
                        planKey,
                        planValue
                    }) : ''


                    return {
                        patternCode: patternCodeValue.toString(),
                        sceneCode: sceneCodeTValue.toString(),
                        plan
                    };
                }
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '名称'
                },
                {
                    header: '开始版本：',
                    dataIndex: 'fromVersion',
                    xtype: 'text',
                    placeholder: '开始版本'
                },
                {
                    header: '结束版本：',
                    dataIndex: 'toVersion',
                    xtype: 'text',
                    placeholder: '结束版本'
                },
                {
                    header: '配置的key：',
                    dataIndex: 'key',
                    xtype: 'textarea',

                    check: 'required',
                    placeholder: '配置的key'
                },
                {
                    header: '配置内容：',
                    dataIndex: 'value',
                    rows: 8,
                    xtype: 'textarea',
                    placeholder: '配置内容'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    rows: 3,
                    xtype: 'textarea',
                    placeholder: '配置说明'
                },
                {
                    header: '车型:',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: [].concat(carTypeArr)
                },
                {
                    header: '科目:',
                    dataIndex: 'kemu',
                    xtype: Plugin('swallow!auto-prompt', {
                        store: [{
                            key: '',
                            value: '选择科目'
                        }].concat(kemuArr),
                        dataIndex: 'kemu',
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '平台:',
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择平台'
                    },

                    {
                        key: 'android',
                        value: 'Android'
                    },
                    {
                        key: 'iphone',
                        value: 'Iphone'
                    }]
                },
                {
                    header: '访问模式:',
                    dataIndex: 'patternCodeT',
                    xtype: 'checkbox',
                    store: [{
                        key: '101',
                        value: '普通模式'
                    }, {
                        key: '102',
                        value: '长辈模式'
                    }]
                },
                {
                    header: '访问场景:',
                    dataIndex: 'sceneCodeT',
                    xtype: 'checkbox',
                    store: [{
                        key: '101',
                        value: '普通场景'
                    }, {
                        key: '102',
                        value: '扣满12分'
                    }]
                },
                // {
                //     header: '方案',
                //     dataIndex: 'plan',
                //     xtype: 'text',
                // },
                {
                    header: '过滤城市code:',
                    dataIndex: 'cityCode',
                    xtype: Plugin('swallow!auto-prompt', {
                        store: getAllCity(),

                        dataIndex: 'cityCode',
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '城市匹配模式:',
                    dataIndex: 'cityType',
                    xtype: 'select',
                    store: [
                        {
                            key: 'gps',
                            value: 'GPS匹配'
                        },
                        {
                            key: 'obscure',
                            value: '模糊匹配'
                        }
                    ]
                },

                {
                    header: '过滤黑名单',
                    dataIndex: 'blackList',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '全部可见'
                    },
                    {
                        key: 'true',
                        value: '黑名单可见'
                    },
                    {
                        key: 'false',
                        value: '白名单可见'
                    },
                    ]
                },
                {
                    header: '策略Key',
                    dataIndex: 'planKey',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '策略Key'
                },
                {
                    header: '策略值',
                    dataIndex: 'planValue',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '策略值'
                }

            ]
        }).add();
    }

    var codeMir = null;

    var list = function (panel) {
        Table({
            description: '远程配置列表',
            title: '远程配置列表',

            search: [
                {
                    header: '名称:',
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '名称'
                },
                {
                    header: 'key:',
                    dataIndex: 'key',
                    xtype: 'text',
                    placeholder: 'key'
                },
                {
                    header: '车型:',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择车型'
                    }].concat(carTypeArr)
                },
                {
                    header: '科目:',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择科目'
                    }].concat(kemuArr)
                },
                {
                    header: '城市匹配模式',
                    dataIndex: 'cityType',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '城市匹配模式'
                    },
                    {
                        key: 'gps',
                        value: 'GPS匹配'
                    },
                    {
                        key: 'obscure',
                        value: '模糊匹配'
                    }
                    ]
                }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 600,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!remote-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '配置的key：',
                            dataIndex: 'key'
                        },
                        {
                            header: '配置内容：',
                            dataIndex: 'value'
                        },
                        {
                            header: '配置说明：',
                            dataIndex: 'remark'
                        },
                        {
                            header: '过滤城市code：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '过滤的城市名称：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '城市匹配模式',
                            dataIndex: 'cityType',
                            render: function (v) {
                                switch (v) {
                                    case 'gps':
                                        return 'GPS匹配';
                                    case 'obscure':
                                        return '模糊匹配'
                                }
                            }
                        },

                        {
                            header: '开始版本：',
                            dataIndex: 'fromVersion',


                        },
                        {
                            header: '结束版本：',
                            dataIndex: 'toVersion',
                        },

                        // {
                        //     header: '版本控制：',
                        //     dataIndex: 'version'
                        // },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                 
                    renderAfter: function (config, dom, data) {
                        let patternCode = data.data.patternCode || '';
                        let sceneCode = data.data.sceneCode || '';
                        let patternCodeArr = patternCode?.split(',');
                        let sceneCodeArr = sceneCode?.split(',');
                        var PDom1 = dom.item('patternCodeT')[0];
                        var PDom2 = dom.item('patternCodeT')[1];
                        var SDom1 = dom.item('sceneCodeT')[0];
                        var SDom2 = dom.item('sceneCodeT')[1];
                        patternCodeArr?.forEach(item => {
                            if (item == PDom1.value) {
                                PDom1.checked = true
                            }

                            if (item == PDom2.value) {
                                PDom2.checked = true
                            }
                        })

                        sceneCodeArr?.forEach(item => {
                            if (item == SDom1.value) {
                                SDom1.checked = true
                            }

                            if (item == SDom2.value) {
                                SDom2.checked = true
                            }
                        })

                        const plan = data.data.plan && JSON.parse(data.data.plan);

                        dom.item('planKey').val(plan?.planKey);
                        dom.item('planValue').val(plan?.planValue);

                        dom.item('value').css({ 'height': 300 + 'px' });
                        var domData = `<div class="form-group" data-item="value-group"> 
                            <label class="col-sm-3 control-label" style="text-align:right;padding-right:30px">配置内容:</label>
                            <textarea class="col-sm-8" id="codemirror"></textarea>
                            <div class="label label-info" style="cursor:pointer;margin-left:100px" id="checkoutBtn">校验JSON</div>
                        </div>`;
                        $(domData).insertBefore(dom.item('remark').parent().parent());
                        setTimeout(() => {
                            codeMir = CodeMirror.fromTextArea(document.getElementById("codemirror"), {
                                lineNumbers: true,
                                matchBrackets: true,
                                autoCloseBrackets: true,
                                styleActiveLine: true,
                                mode: 'text/javascript',
                                lineWrapping: true,
                            });
                            try {
                                codeMir.setValue(JSON.stringify(JSON.parse(data.data.value), null, 4));
                            } catch {
                                codeMir.setValue(data.data.value);
                            }
                            codeMir.on('change', function () {
                                $('#codemirror').val(codeMir.getValue());
                            });

                            $('#checkoutBtn').on('click', function () {
                                let json = codeMir.getValue();
                                var value = isJson(json);
                                isJsonValue = value;

                                value ? Widgets.dialog.alert('JSON格式正确') : Widgets.dialog.alert('请输入正确的JSON格式')
                            })

                        }, 200)
                    },
                    form: {
                        submitHandler: function (form) {
                            var value = codeMir.getValue();
                            var isJsonValue = isJson(value);

                            if (!isJsonValue) {
                                Widgets.dialog.alert('请输入正确的JSON格式');
                                return;
                            }
                            var patternCodeValue = []
                            $(form).item('patternCodeT')[0].checked && patternCodeValue.push($(form).item('patternCodeT')[0].value);
                            $(form).item('patternCodeT')[1].checked && patternCodeValue.push($(form).item('patternCodeT')[1].value);

                            var sceneCodeTValue = [];
                            $(form).item('sceneCodeT')[0].checked && sceneCodeTValue.push($(form).item('sceneCodeT')[0].value);
                            $(form).item('sceneCodeT')[1].checked && sceneCodeTValue.push($(form).item('sceneCodeT')[1].value);


                            var JSONValue = JSON.stringify(JSON.parse(value), null, 0)

                            const planKey = $(form).item('planKey').val()
                            const planValue = $(form).item('planValue').val()
        
                            const plan = (planKey || planValue)  ? JSON.stringify({
                                planKey,
                                planValue
                            }) : ''
        
                            console.log(plan,'planplan');
                            return {
                                patternCode: patternCodeValue.toString(),
                                sceneCode: sceneCodeTValue.toString(),
                                value: JSONValue ? JSONValue : value,
                                plan
                            };
                        }
                    },
                    store: {
                        load: 'swallow!remote-config/data/view',
                        save: 'swallow!remote-config/data/update'
                    },

                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '名称'
                        },
                        {
                            header: '开始版本：',
                            dataIndex: 'fromVersion',
                            xtype: 'text',
                            placeholder: '开始版本',

                        },
                        {
                            header: '结束版本：',
                            dataIndex: 'toVersion',
                            xtype: 'text',
                            placeholder: '结束版本',

                        },




                        {
                            header: '车型:',
                            dataIndex: 'carType',
                            xtype: 'checkbox',
                            store: [].concat(carTypeArr)
                        },
                        {
                            header: '科目:',
                            dataIndex: 'kemu',
                            // store: [{
                            //     key: '',
                            //     value: '选择科目'
                            // }].concat(kemuArr)

                            xtype: Plugin('swallow!auto-prompt', {
                                store: [{
                                    key: '',
                                    value: '选择科目'
                                }].concat(kemuArr),
                                dataIndex: 'kemu',
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                        },
                        {
                            header: '平台:',
                            dataIndex: 'platform',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '选择平台'
                            },
                            {
                                key: 'android',
                                value: 'Android'
                            },
                            {
                                key: 'iphone',
                                value: 'Iphone'
                            }
                            ]

                        },

                        {
                            header: '访问模式:',
                            dataIndex: 'patternCodeT',
                            xtype: 'checkbox',
                            store: [{
                                key: '101',
                                value: '普通模式'
                            }, {
                                key: '102',
                                value: '长辈模式'
                            }]
                        },
                        {
                            header: '访问场景:',
                            dataIndex: 'sceneCodeT',
                            xtype: 'checkbox',
                            store: [{
                                key: '101',
                                value: '普通场景'
                            }, {
                                key: '102',
                                value: '扣满12分'
                            }]
                        },
                        {
                            header: '过滤城市code:',
                            dataIndex: 'cityCode',
                            xtype: Plugin('swallow!auto-prompt', {
                                store: getAllCity(),

                                dataIndex: 'cityCode',
                                // index: {
                                //
                                //     key: 'code',
                                //     value: 'name',
                                //     search: 'name'
                                // },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                        },

                        {
                            header: '城市匹配模式:',
                            dataIndex: 'cityType',
                            xtype: 'select',
                            store: [
                                {
                                    key: 'gps',
                                    value: 'GPS匹配'
                                },
                                {
                                    key: 'obscure',
                                    value: '模糊匹配'
                                }
                            ]
                        },



                        {
                            header: '过滤黑名单',
                            dataIndex: 'blackList',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '全部可见'
                            },
                            {
                                key: 'true',
                                value: '黑名单可见'
                            },
                            {
                                key: 'false',
                                value: '白名单可见'
                            },
                            ]
                        },

                        {
                            header: '配置的key：',
                            dataIndex: 'key',
                            xtype: 'textarea',

                            check: 'required',
                            placeholder: '配置的key'
                        },
                        {
                            header: '配置说明：',
                            dataIndex: 'remark',
                            xtype: 'textarea',
                            placeholder: '配置说明'
                        },
                        {
                            header: '策略Key',
                            dataIndex: 'planKey',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '策略Key'
                        },
                        {
                            header: '策略值',
                            dataIndex: 'planValue',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '策略值'
                        }
                        
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!remote-config/data/delete'
                },
                {
                    name: '投放策略',
                    class: 'success',
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Plugin('simple!product-filter', {
                            store: 'swallow!remote-config/data/filter?id=' + lineData.id
                        }).render().done(function () {
                            // Store(['sirius!goods-session/data/updateAdverts']).load();
                            table.render();
                        });
                    }
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        EdPersonas.editPersonas(table, lineData)
                    },
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 1 ? name : '';

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认正式发布吗', function (e, stat) {
                            if (stat) {

                                Store(['swallow!remote-config/data/update?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }

                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 1 ? '' : name;

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认测试发布吗', function (e, stat) {
                            if (stat) {
                                Store(['swallow!remote-config/data/update?id=' + lineData.id + '&status=1']).save().done(data => {
                                    table.render();
                                    console.log(data)
                                }).fail();
                            }
                        })

                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name',
                    width:150
                },
                {
                    header: '配置的key',
                    dataIndex: 'key'
                },
                {
                    header: '配置内容',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(Widgets.dialog.html, '125');
                        Widgets.dialog.html('配置内容', { width: 1200 }).done(function (dialog) {
                            var data = JSON.stringify(JSON.parse(lineData.value), null, 4)
                            var remark = ''
                            if(lineData.remark){
                               remark+=`<pre style="max-height: 200px; overflow: auto">${lineData.remark}</pre>` 
                            }
                            $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>'+remark)
                        })
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data?.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        var dataArr = data?.split(',');
                        var text = '';
                        dataArr?.forEach((item) => {
                            console.log(item, kemuMap[item], 'item');
                            text += item && kemuMap[item];
                        })
                        return text;
                    }
                },
                {
                    header: '平台',
                    dataIndex: 'platform',
                },
                {
                    header: '业务类型',
                    dataIndex: 'typeCode',
                    render: function (data) {
                        return typeCodeMap[data]
                    }
                },
                {
                    header: '方案',
                    dataIndex: 'plan',
                },
                {
                    header: '过滤的城市名称',
                    dataIndex: 'cityCode',
                    width: '160',
                    render: function (data) {
                        var str = '';
                        if (data) {
                            var data = data?.split(',')
                            for (var i = 0; i < data.length; ++i) {
                                str += '<span style="background-color: #8cc5ff;margin-left:5px;border-radius: 3px;">' + District2.getNameByCode(data[i]) + '</span>'
                            }
                            return str;
                        } else {
                            return '';
                        }
                    }
                },
                {
                    header: '城市匹配模式',
                    dataIndex: 'cityType',
                    render: function (v) {
                        switch (v) {
                            case 'gps':
                                return 'GPS匹配';
                            case 'obscure':
                                return '模糊匹配'
                        }
                    }
                },
                {
                    header: '开始版本',
                    dataIndex: 'fromVersion',


                },
                {
                    header: '结束版本',
                    dataIndex: 'toVersion',

                },
                {
                    header: '过滤黑名单',
                    dataIndex: 'blackList',
                    render: function (data) {
                        if (data == null) {
                            return '全部可见'
                        } else {
                            return data ? '黑名单可见' : '白名单可见'
                        }
                    }
                },
                {
                    header: '发布状态',
                    dataIndex: 'status',
                    render: function (data) {
                        if (data == 1) {
                            return '测试发布'
                        } else if (data == 2) {
                            return '正式发布'
                        } else if(data == 0){
                            return '未发布'
                        }
                    }

                },

                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }

                    ,
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render:

                        function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }

                    ,
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        },
            ['swallow!remote-config/data/list?typeCode=0'], panel, null
        ).render();
    }

    return {
        list: list
    }

});
