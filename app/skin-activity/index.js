/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools'], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = 'activity'
    var dataType = 'array'
    var tempData = []

    let blackList = ['updateTime', 'updateUserId', 'updateUserName','createTime', 'createUserId', 'createUserName', 'id', 'skinId', 'index']
    function getDataFromObj(obj, blackList) {
        let newObj = {}
        for(let key in obj) {
            if(blackList.indexOf(key) === -1){
                newObj[key] = obj[key]
            }
        }
        return newObj;
    }

    var list = function(panel, routeData) {
        skinId = routeData.id
        submitHandler = Tools.genSubmitHandler(skinId, bucket, dataType, tempData)
        var name = routeData.name || ''
        Table({
            // description: name+':activity列表',
            title: name+':activity列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    renderAfter: function (config, dom) {
                        tempData = config.data.data.map(item => {
                            return getDataFromObj(item, blackList)
                        })
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&dataType=${dataType}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            dataIndex: 'index',
                            xtype: 'hidden'
                        },
                        {
                            header: '开始时间：',
                            dataIndex: 'beginDate',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            xtype: 'date',
                            check: 'required',
                            placeholder: '开始时间'
                        },
                        {
                            header: '过期时间：',
                            dataIndex: 'expiredDate',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            xtype: 'date',
                            check: 'required',
                            placeholder: '过期时间'
                        },
                        {
                            header: 'tab标识：',
                            dataIndex: 'uniqueKey',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'tab标识'
                        },
                        {
                            header: 'icon：',
                            dataIndex: 'icon',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'icon',
                                uploadIndex: 'icon',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: 'icon'
                        },
                        {
                            header: 'bubble：',
                            dataIndex: 'bubble',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'bubble',
                                uploadIndex: 'bubble',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: 'bubble'
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'beginDate'
                },
                {
                    header: '过期时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'expiredDate'
                },
                {
                    header: 'tab标识',
                    dataIndex: 'uniqueKey'
                },
                {
                    header: 'icon',
                    dataIndex: 'icon'
                },
                {
                    header: 'bubble',
                    dataIndex: 'bubble'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&dataType=${dataType}&id=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});