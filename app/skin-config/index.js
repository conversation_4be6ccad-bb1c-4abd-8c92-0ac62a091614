/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'swallow!skin-config/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '名称'
                },
                {
                    header: '我的页面的顶部：',
                    dataIndex: 'mineTopBg',
                    xtype: 'textarea',
                    maxlength: 256,
                    check: 'required',
                    placeholder: '我的页面的顶部'
                }
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: 'skin-config列表',
            title: 'skin-config列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!skin-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '我的页面的顶部：',
                            dataIndex: 'mineTopBg'
                        }
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'swallow!skin-config/data/view',
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '名称'
                        },
                        {
                            header: '我的页面的顶部：',
                            dataIndex: 'mineTopBg',
                            xtype: 'textarea',
                            maxlength: 256,
                            check: 'required',
                            placeholder: '我的页面的顶部'
                        }
                    ]
                },
                {
                    name: '主页顶部',
                    class: 'danger',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-top-bg-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-top-bg-' + lineData.id,
                                    name: lineData.name + ':主页顶部'
                                })
                            }
                            require(['swallow!app/skin-home-top-bg/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '8+2的中间大圆-ExamKemu4',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-center-entrance-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-center-entrance-' + lineData.id,
                                    name: lineData.name + ':8+2的中间大圆-ExamKemu4'
                                })
                            }
                            require(['swallow!app/skin-home-center-entrance/index'], function (Item) {
                                let config = {bucket: 'homeCenterEntranceExamKemu4', name: 'ExamKemu4'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '8+2的中间大圆-ExamKemu1',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-center-entrance-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-center-entrance-' + lineData.id,
                                    name: lineData.name + ':8+2的中间大圆-ExamKemu1'
                                })
                            }
                            require(['swallow!app/skin-home-center-entrance/index'], function (Item) {
                                let config = {bucket: 'homeCenterEntranceExamKemu1', name: 'ExamKemu1'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '8+2的中间大圆-PracticeKemu4',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-center-entrance-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-center-entrance-' + lineData.id,
                                    name: lineData.name + ':8+2的中间大圆-PracticeKemu4'
                                })
                            }
                            require(['swallow!app/skin-home-center-entrance/index'], function (Item) {
                                let config = {bucket: 'homeCenterEntrancePracticeKemu4', name: 'PracticeKemu4'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '8+2的中间大圆-PracticeKemu1',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-center-entrance-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-center-entrance-' + lineData.id,
                                    name: lineData.name + ':8+2的中间大圆-PracticeKemu1'
                                })
                            }
                            require(['swallow!app/skin-home-center-entrance/index'], function (Item) {
                                let config = {bucket: 'homeCenterEntrancePracticeKemu1', name: 'PracticeKemu1'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '底部的tabbar',
                    class: 'success',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-tab-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-tab-' + lineData.id,
                                    name: lineData.name + ':底部的tabbar'
                                })
                            }
                            require(['swallow!app/skin-home-tab-bar/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '底部的tabbar-tab',
                    class: 'success',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-tab-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-tab-' + lineData.id,
                                    name: lineData.name + ':底部的tabbar-tab'
                                })
                            }
                            require(['swallow!app/skin-tab/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '底部的tabbar-活动',
                    class: 'success',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-activity-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-activity-' + lineData.id,
                                    name: lineData.name + ':底部的tabbar-activity'
                                })
                            }
                            require(['swallow!app/skin-activity/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '8+2的8个icon-科一',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-icons-kemu-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-icons-kemu-' + lineData.id,
                                    name: lineData.name + ':8+2的8个icon'
                                })
                            }
                            require(['swallow!app/skin-home-icons-kemu/index'], function (Item) {
                                let config = {bucket: 'homeIconsKemu1', name: '科一'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '8+2的8个icon-科四',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('skin-home-icons-kemu-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'skin-home-icons-kemu-' + lineData.id,
                                    name: lineData.name + ':8+2的8个icon'
                                })
                            }
                            require(['swallow!app/skin-home-icons-kemu/index'], function (Item) {
                                let config = {bucket: 'homeIconsKemu4', name: '科四'}
                                Item.list(nPanel, lineData, config)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!skin-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '我的页面的顶部',
                    dataIndex: 'mineTopBg'
                }
            ]
        }, ['swallow!skin-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});