/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools',], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = ''

    var list = function(panel, routeData, routeConfig) {
        skinId = routeData.id
        bucket = routeConfig.bucket
        submitHandler = Tools.genSubmitHandler(skinId, bucket)
        var name = routeData.name
        Table({
            // description: name+':8+2的中间大圆列表-' + routeConfig.name,
            title: name+':8+2的中间大圆列表-' + routeConfig.name,
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '按钮的颜色：',
                            dataIndex: 'colors',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '按钮的颜色'
                        },
                        {
                            header: '进度条：',
                            dataIndex: 'progressColors',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '进度条'
                        },
                        {
                            header: '外圈的刻度的图片：',
                            dataIndex: 'outlineImage',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'outlineImage',
                                uploadIndex: 'outlineImage',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '外圈的刻度的图片'
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '图片'
                        },
                        {
                            header: '默认忽略配置的图片：',
                            dataIndex: 'prefersAdvertImage',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'prefersAdvertImage',
                                uploadIndex: 'prefersAdvertImage',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '默认忽略配置的图片'
                        },
                        {
                            header: '链接：',
                            dataIndex: 'url',
                            xtype: 'textarea',
                            maxlength: 512,
                            check: 'required',
                            placeholder: '链接'
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '按钮的颜色',
                    dataIndex: 'colors'
                },
                {
                    header: '进度条',
                    dataIndex: 'progressColors'
                },
                {
                    header: '外圈的刻度的图片',
                    dataIndex: 'outlineImage'
                },
                {
                    header: '图片',
                    dataIndex: 'image'
                },
                {
                    header: '默认忽略配置的图片',
                    dataIndex: 'prefersAdvertImage'
                },
                {
                    header: '链接',
                    dataIndex: 'url'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&id=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});