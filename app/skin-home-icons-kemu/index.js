/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools'], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = ''
    var dataType = 'array'
    var tempData = []

    let blackList = ['updateTime', 'updateUserId', 'updateUserName','createTime', 'createUserId', 'createUserName', 'id', 'skinId', 'index']
    function getDataFromObj(obj, blackList) {
        let newObj = {}
        for(let key in obj) {
            if(blackList.indexOf(key) === -1){
                newObj[key] = obj[key]
            }
        }
        return newObj;
    }

    var list = function(panel, routeData, routeConfig) {
        skinId = routeData.id
        bucket = routeConfig.bucket
        submitHandler = Tools.genSubmitHandler(skinId, bucket, dataType, tempData)
        var name = routeData.name
        Table({
            // description: name+':8+2的8个icon列表-' + routeConfig.name,
            title: name+':8+2的8个icon列表-' + routeConfig.name,
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    renderAfter: function (config, dom) {
                        tempData = config.data.data.map(item => {
                            return getDataFromObj(item, blackList)
                        })
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&dataType=${dataType}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            dataIndex: 'index',
                            xtype: 'hidden'
                        },
                        {
                            header: 'displayOrder：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            check: 'required',
                            placeholder: 'displayOrder'
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '图片'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '标题'
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'clickUrl',
                            xtype: 'textarea',
                            maxlength: 512,
                            check: 'required',
                            placeholder: '跳转链接'
                        },
                        {
                            header: 'redDotText：',
                            dataIndex: 'redDotText',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'redDotText'
                        },
                        {
                            header: 'redDotShow：',
                            dataIndex: 'redDotShow',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'displayOrder',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '图片',
                    dataIndex: 'image'
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '跳转链接',
                    dataIndex: 'clickUrl'
                },
                {
                    header: 'redDotText：',
                    dataIndex: 'redDotText'
                },
                {
                    header: 'redDotShow：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'redDotShow'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&dataType=${dataType}&id=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});