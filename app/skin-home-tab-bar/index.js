/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools'], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = 'homeTabBar'

    var list = function(panel, routeData) {
        skinId = routeData.id
        submitHandler = Tools.genSubmitHandler(skinId, bucket)
        var name = routeData.name
        Table({
            // description: name+':底部的tabbar列表',
            title: name+':底部的tabbar列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: 'tab栏背景图：',
                            dataIndex: 'tabBackground',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'tabBackground',
                                uploadIndex: 'tabBackground',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: 'tab栏背景图'
                        },
                        {
                            header: '未选中tab文案颜色：',
                            dataIndex: 'normalTextColor',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '未选中tab文案颜色'
                        },
                        {
                            header: '选中的tab文案颜色：',
                            dataIndex: 'selectedTextColor',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '选中的tab文案颜色'
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'tab栏背景图',
                    dataIndex: 'tabBackground'
                },
                {
                    header: '未选中tab文案颜色',
                    dataIndex: 'normalTextColor'
                },
                {
                    header: '选中的tab文案颜色',
                    dataIndex: 'selectedTextColor'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&id=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});