/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools'], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = 'homeTopBg'

    var list = function(panel, routeData) {
        skinId = routeData.id
        submitHandler = Tools.genSubmitHandler(skinId, bucket)
        var name = routeData.name
        Table({
            // description: name+':主页顶部列表',
            title: name+':主页顶部列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '顶部背景图：',
                            dataIndex: 'image',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '顶部背景图'
                        },
                        {
                            header: '消息盒子普通状态图片：',
                            dataIndex: 'msgBoxNormalImage',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'msgBoxNormalImage',
                                uploadIndex: 'msgBoxNormalImage',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '消息盒子普通状态图片'
                        },
                        {
                            header: '消息盒子有新消息图片：',
                            dataIndex: 'msgBoxReceiveMsgImage',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'msgBoxReceiveMsgImage',
                                uploadIndex: 'msgBoxReceiveMsgImage',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '消息盒子有新消息图片'
                        },
                        {
                            header: '导航栏着色颜色：',
                            dataIndex: 'navigationBarTintColor',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '导航栏着色颜色'
                        },
                        {
                            header: '状态栏是否显示为白色：',
                            dataIndex: 'statusBarLight',
                            xtype: 'radio',
                            store: [
                                {
                                    key: 'true',
                                    value: '是'
                                },
                                {
                                    key: 'false',
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '标签普通状态颜色：',
                            dataIndex: 'tabNormalColor',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '标签普通状态颜色'
                        },
                        {
                            header: '标签文字选中态颜色：',
                            dataIndex: 'tabSelectedColor',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '标签文字选中态颜色'
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '顶部背景图',
                    dataIndex: 'image'
                },
                {
                    header: '消息盒子普通状态图片',
                    dataIndex: 'msgBoxNormalImage'
                },
                {
                    header: '消息盒子有新消息图片',
                    dataIndex: 'msgBoxReceiveMsgImage'
                },
                {
                    header: '导航栏着色颜色',
                    dataIndex: 'navigationBarTintColor'
                },
                {
                    header: '状态栏是否显示为白色',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'statusBarLight'
                },
                {
                    header: '标签普通状态颜色',
                    dataIndex: 'tabNormalColor'
                },
                {
                    header: '标签文字选中态颜色',
                    dataIndex: 'tabSelectedColor'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&skinId=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});