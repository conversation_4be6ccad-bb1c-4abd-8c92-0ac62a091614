/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'swallow!common/tools'], function(Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {
    var skinId
    var submitHandler
    var bucket = 'tab'
    var dataType = 'array'
    var tempData = []

    let blackList = ['updateTime', 'updateUserId', 'updateUserName','createTime', 'createUserId', 'createUserName', 'id', 'skinId', 'index']
    function getDataFromObj(obj, blackList) {
        let newObj = {}
        for(let key in obj) {
            if(blackList.indexOf(key) === -1){
                newObj[key] = obj[key]
            }
        }
        return newObj;
    }

    var list = function(panel, routeData) {
        skinId = routeData.id
        submitHandler = Tools.genSubmitHandler(skinId, bucket, dataType, tempData)
        var name = routeData.name || ''
        Table({
            // description: name+':tab列表',
            title: name+':tab列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    renderAfter: function (config, dom) {
                        tempData = config.data.data.map(item => {
                            return getDataFromObj(item, blackList)
                        })
                    },
                    store: {
                        load: `swallow!skin-config/data/view?shut=${bucket}&dataType=${dataType}&id=${skinId}`,
                        save: 'swallow!skin-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            dataIndex: 'index',
                            xtype: 'hidden'
                        },
                        {
                            header: 'tab标识：',
                            dataIndex: 'uniqueKey',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'tab标识'
                        },
                        {
                            header: 'pageUrl：',
                            dataIndex: 'pageUrl',
                            xtype: 'textarea',
                            maxlength: 512,
                            check: 'required',
                            placeholder: 'pageUrl'
                        },
                        {
                            header: 'text：',
                            dataIndex: 'text',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: 'text'
                        },
                        {
                            header: '未选中的图片：',
                            dataIndex: 'normal',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'normal',
                                uploadIndex: 'normal',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '未选中的图片'
                        },
                        {
                            header: '选中时的图片IOS：',
                            dataIndex: 'selectedIOS',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'selectedIOS',
                                uploadIndex: 'selectedIOS',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '选中时的图片IOS'
                        },
                        {
                            header: '选中时的图片：',
                            dataIndex: 'selected',
                            xtype: Plugin('swallow!upload', {
                                dataIndex: 'selected',
                                uploadIndex: 'selected',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传图片',
                                url: 'simple-upload2://admin-upload.htm'
                            }, function () {
                            }),
                            check: 'required',
                            placeholder: '选中时的图片'
                        }
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'tab标识',
                    dataIndex: 'uniqueKey'
                },
                {
                    header: 'pageUrl：',
                    dataIndex: 'pageUrl'
                },
                {
                    header: 'text',
                    dataIndex: 'text'
                },
                {
                    header: '未选中的图片',
                    dataIndex: 'normal'
                },
                {
                    header: '选中时的图片IOS',
                    dataIndex: 'selectedIOS'
                },
                {
                    header: '选中时的图片',
                    dataIndex: 'selected'
                }
            ]
        }, [`swallow!skin-config/data/list?shut=${bucket}&dataType=${dataType}&id=${skinId}`], panel, null).render();
    }

    return {
        list: list
    }

});