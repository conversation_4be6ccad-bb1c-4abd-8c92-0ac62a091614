/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

   
    define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'swallow!app/comm/tiku'], function(Template, Table, Utils, Widgets, Store, Form,TIKU) {
    var statusMap = {
        '-1': '下线',
        1: '测试发布',
        2: '发布'
    };
        
        var statusArr = mapToArray(statusMap);

    var patchTypeMap = {
        1:'科目3路线视频' ,2: '科目2项目视频'  ,3: '科目3项目视频', 4: '科二考场详情页重难点'
    }

    var patchVideoTypeMap = {
        1:'试看视频' ,2:'完整视频'
    }
    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'swallow!video-patch/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '贴片类型',
                    dataIndex: 'patchType',
                    xtype: 'select',
                    store:'swallow!video-patch/data/typeList'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store:  [{key:'',value:'请选择车型'}].concat(carTypeArr),
                },
                {
                    header: '视频ID',
                    dataIndex: 'patchVideoId',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '视频ID'
                },
                {
                    header: '被贴片视频的类型：',
                    dataIndex: 'patchVideoType',
                    xtype: 'select',
                    store:'swallow!video-patch/data/videoTypeList',
                    check: 'required'
                },
                {
                    header: '备注：',
                    dataIndex: 'remark',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '备注'
                },
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }
    }

    var list = function(panel) {
        Table({
            description: '视频贴片列表',
            title: '视频贴片列表',
            search: [
                {
                    placeholder: '贴片类型',
                    dataIndex: 'patchType',
                    xtype: 'select',
                    store: 'swallow!video-patch/data/typeList',
                    insert: [{
                            key: '',
                            value: '请选择贴片类型'
                    }]
                },
                {
                    placeholder: '被贴片视频的类型',
                    dataIndex: 'patchVideoType',
                    xtype: 'select',
                    store:'swallow!video-patch/data/videoTypeList',
                    insert: [{
                        key: '',
                        value: '请选择被贴片视频的类型'
                    }]
                },
                {
                    placeholder: '车型',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{key:'',value:'请选择车型'}].concat(carTypeArr),
                },
                {
                    placeholder: '视频ID',
                    dataIndex: 'patchVideoId',
                    xtype: 'text',
                },
                {
                    placeholder: '状态',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{ key: '', value:'请选择状态'}].concat(statusArr),
                }
            ],
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table);
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'swallow!video-patch/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '贴片类型',
                            dataIndex: 'patchType',
                            render: function (data) {
                               return patchTypeMap[data]
                            }
                        },
                        {
                            header: '车型',
                            dataIndex: 'carType',
                            render:function(data){
                                return TIKU[data]
                            }
                        },
                        {
                            header: '视频ID',
                            dataIndex: 'patchVideoId'
                        },
                        {
                            header: '被贴片视频的类型',
                            dataIndex: 'patchVideoType',
                            render: function (data) {
                               return patchVideoTypeMap[data]
                            }
                        },
                        {
                            header: '状态',
                            dataIndex: 'status',
                            render: function (data) {
                                return statusMap[data]
                            }
                        },
                        {
                            header: '备注',
                            dataIndex: 'remark'
                        },
                        {
                            header: '播放地址',
                            dataIndex: 'playUrl',
                            render: function (data) {
                                if (data) {
                                    return `<a href="${data}" target="_blank">点击播放</a>`
                                } else {
                                    return ''
                                }
                            }
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                         },
                         {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新人',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更新时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }
                    ]
                },
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    class: 'info',
                    render: function (name, arr, index) {
                        const status = arr[index].status
                        if (status == -1) {
                            return '测试发布';
                        } else if (status == 1) {
                            return '发布';
                        }else if (status == 2) {
                            return '禁用';
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(lineData, 'lineData');
                        const status = lineData.status == -1 ? 1 : lineData.status == 1 ? 2 : -1;

                        let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定禁用吗?' : '确定测试发布吗?'
                        Widgets.dialog.confirm(title, function (e, confirm) {
                            if (confirm) {
                                Store(['swallow!video-patch/data/update']).save([{
                                    params: {
                                        id: lineData.id,
                                        status
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'swallow!video-patch/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '贴片类型',
                         dataIndex: 'patchType',
                         render: function (data) {
                            return patchTypeMap[data]
                         }
                     },
                     {
                         header: '车型',
                         dataIndex: 'carType',
                         render: function (data) {
                            return TIKU[data]
                         }
                     },
                     {
                         header: '视频ID',
                         dataIndex: 'patchVideoId'
                     },
                     {
                         header: '被贴片视频的类型',
                         dataIndex: 'patchVideoType',
                         render: function (data) {
                            return patchVideoTypeMap[data]
                         }
                     },
                     {
                         header: '状态',
                         dataIndex: 'status',
                         render: function (data) {
                             return statusMap[data]
                         }
                     },
                     {
                         header: '备注',
                         dataIndex: 'remark'
                },
                {
                    header: '播放地址',
                    dataIndex: 'playUrl',
                    render: function (data) {
                        if (data) {
                            return `<a href="${data}" target="_blank">点击播放</a>`
                        } else {
                            return ''
                        }
                    }
                },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                        },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }
            ]
        }, ['swallow!video-patch/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});