"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'swallow!common/tools'],
    function (Template, Table, Utils, Tools) {
        var kemuStore = [
            {
                key: '',
                value: '请选择科目'
            },
            {
                key: 1,
                value: '科目一'
            },
            {
                key: 2,
                value: '科目二'
            },
            {
                key: 3,
                value: '科目三'
            },
            {
                key: 4,
                value: '科目四'
            }
        ]
        var kemuMap = Tools.getMapfromArray(kemuStore);
        var HCEPTypeStore = [
            {
                key: '',
                value: '请选择类型'
            },
            {
                key: 1,
                value: '科一练习'
            },
            {
                key: 2,
                value: '科一考试'
            },
            {
                key: 3,
                value: '科四练习'
            },
            {
                key: 4,
                value: '科四考试'
            }
        ]
        var HCEPTypeMap = Tools.getMapfromArray(HCEPTypeStore);
        return {
            kemuStore,
            kemuMap,
            HCEPTypeStore,
            HCEPTypeMap
        };
    });