"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils'],
    function (Template, Table, Utils) {
        var getMapfromArray = function (store) {
            var obj = {};
            store.forEach(item => { obj[item.key] = item.value })
            return obj;
        }

        var genSubmitHandler = function(skinId, bucket, dataType, tempData) {
            return function (form, formSubmit) {
                let data = {}
                $(form).serializeArray().filter(item => item.name !== 'id').forEach(item => {
                    data[item.name] = item.value
                })
                let parmas;
                if(dataType == 'array') {
                    let index = data.index;
                    delete data.index
                    tempData[index] = data;
                    parmas = {
                        id: skinId,
                        [bucket]: JSON.stringify({[bucket]: tempData})
                    }
                }else{
                    parmas = {
                        id: skinId,
                        [bucket]: JSON.stringify({[bucket]: data})
                    }
                }
                $(form.elements).filter('[name]').attr('data-disabled', '')
                formSubmit(parmas)
            }
        }
        return {
            getMapfromArray,
            genSubmitHandler
        };
    });