﻿/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var _group = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    var zimu = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'];
    
    _group.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            let addIndex = 0;

            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
            } else {
                valueArr = [{ desc: '', name: zimu[0], ratio: '' }]
            }

            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-goods-session-group-highlights').on('click', function (e) {
                        addIndex = zimu.indexOf(valueArr.at(-1).name)


                        addIndex++;
                        valueArr.push({ desc: '', name: zimu[addIndex], ratio: '' })
                        renderTemplate()
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        console.log(index + 1,valueArr);
                        if (+index + 1 != valueArr.length) {
                            alert('请依次删除');
                            return
                        }
                        addIndex--;
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    dom.item('name').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].name = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('desc').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].desc = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })

                    dom.item('ratio').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].ratio = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex
                    }
                }).render()
            }

        }
    }

    _group.prototype.constructor = _group;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new _group(plugin, success);
    }

});