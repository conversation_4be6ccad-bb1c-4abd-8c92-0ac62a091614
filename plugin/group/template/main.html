﻿<script id="swallow-plugin-group-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
      var disabled = config.disabled;
      var zimu = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'];

    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="rateDistributionCopy" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">
        <input type="text" data-item="name" data-index="<?=index?>"
            class="form-control group-name<?=index?> group-input" id="name" placeholder="分组名称"
            value="<?=valueArr[index].name || zimu&&zimu[index]&&zimu[index]?>">
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <input type="text" data-item="desc" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="分组描述"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].desc?>" id="desc">

            <input type="text" data-item="ratio" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="分组比例"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].ratio?>" id="ratio">
        </div>
    </div>
    <?if(index==0){?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-primary"
            data-item="add-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-plus"></span></button></div>
    <?}else{?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-danger"
            data-item="remove-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-remove"></span></button></div>
    <?}?>

</div>
<?}?>



</script>