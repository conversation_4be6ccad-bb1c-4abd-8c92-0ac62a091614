"use strict";
define(['simple!core/utils', 'swallow!plugin/select-district/district3'], function (Utils, District3) {
    var list = District3.list;
    var zxCitys = ['120000', '110000', '500000', '310000'];
    var getDistrict = function () {
        var result = [];
        for (var i = 0; i < list.length; i++) {
            var city = list[i].cities || [];


            var item = {
                province: {
                    name: list[i].name,
                    code: list[i].code,
                },
                citys: []
            };
            for (var n = 0; n < city.length; n++) {
                if (city[n].counties.length == 0) {
                    city[n].counties = [{
                        "code": city[n].code,
                        "name": city[n].name,
                        "parent": city[n].code,
                        "pinyin": city[n].pinyin,
                        "simpleName": city[n].simpleName,
                        "type": "COUNTY"
                    }]
                }
                item.citys.push({
                    name: city[n].name,
                    code: city[n].code,
                    counties: city[n].counties
                });
            }

            if (zxCitys.indexOf(item.province.code) > -1) {
                item.citys = [{
                    name: item.province.name,
                    code: item.province.code,
                    counties: list[i].cities
                }]
            }


            result.push(item)
        }

        return result;
    }
    var district = getDistrict();

    var getProAndCityByAreaCode = function (areaCode) {
        var data = {
            provinceCode: '',
            cityCode: ''
        }
        for (var i = 0; i < district.length; i++) {
            var cities = district[i].citys;
            if (cities && cities.length) {
                for (var j = 0; j < cities.length; j++) {
                    var counties = cities[j].counties;
                    for (var k = 0; k < counties.length; k++) {
                        if (counties[k].code == areaCode) {
                            data.provinceCode = district[i].province.code;
                            data.cityCode = cities[j].code

                            return data;
                        }
                    }
                }
            }
        }
    }


    var getProvinceOfCity = function (city) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            for (var j = 0; j < district[i].citys.length; j++)
                if (district[i].citys[j].code === city)
                    list = district[i].province
        return list;
    }

    var getCitysOfProvince = function (province) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            if (district[i].province.code === province)
                list = district[i].citys;
        return list
    }

    var getAreasOfCity = function (cityCode) {
        var areas = [];
        for (var i = 0; i < district.length; i++) {

            // if(zxCitys.indexOf(list[i].code)>-1){
            //     if(list[i].code==areaCode){
            //         areas = list[i].cities;
            //     }
            // }else{
            var cities = district[i].citys;
            if (cities && cities.length) {
                for (var j = 0; j < cities.length; j++) {
                    if (cities[j].code == cityCode) {
                        areas = cities[j].counties;
                    }
                }
            }
            // }
        }
        return areas
    }

    // console.log(getAreasOfCity(110000));

    var provinces = function () {
        var list = [];
        for (var i = 0; i < district.length; i++) {
            list.push(district[i].province);
        }
        return list;
    }

    var getNameByCode = function (code) {
        for (var i = 0; i < district.length; i++) {
            if (district[i].province.code === code) {
                return district[i].province.name;
            }

            for (var j = 0; j < district[i].citys.length; j++) {
                if (district[i].citys[j].code === code) {
                    return district[i].citys[j].name;
                }
            }
        }
    }

    return {
        list: district,
        provinces: provinces,
        getProvinceOfCity: getProvinceOfCity,
        getCitysOfProvince: getCitysOfProvince,
        getNameByCode: getNameByCode,
        getAreasOfCity: getAreasOfCity,
        getProAndCityByAreaCode: getProAndCityByAreaCode
    }

});
