"use strict";
define(['simple!core/utils', 'swallow!plugin/select-district/district3'], function (Utils, District3) {
    var list = District3.list;

    var getDistrict = function () {
        var result = [];
        for (var i = 0; i < list.length; i++) {
            var city = list[i].cities || [];
            var item = {
                province: {
                    name: list[i].name,
                    code: list[i].code,
                },
                citys: []
            };
            if (/110000|310000|500000|120000/.test(list[i].code)) {
                item = {
                    province: {
                        name: list[i].name,
                        code: list[i].code,
                    },
                    citys: [{
                        name: list[i].name,
                        code: list[i].code,
                    }]
                };
            } else {
                for (var n = 0; n < city.length; n++) {
                    item.citys.push({
                        name: city[n].name,
                        code: city[n].code,
                    });
                }
            }
            result.push(item)
        }
        return result;
    }
    var district = getDistrict();
    var getProvinceOfCity = function (city) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            for (var j = 0; j < district[i].citys.length; j++)
                if (district[i].citys[j].code === city)
                    list = district[i].province;
        return list;
    }

    var getCitysOfProvince = function (province) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            if (district[i].province.code === province)
                list = district[i].citys;
        return list
    }

    var provinces = function () {
        var list = [];
        for (var i = 0; i < district.length; i++) {
            list.push(district[i].province);
        }
        return list;
    }

    var getNameByCode = function (code) {
        for (var i = 0; i < district.length; i++) {
            if (district[i].province.code === code) {
                return district[i].province.name;
            }

            for (var j = 0; j < district[i].citys.length; j++) {
                if (district[i].citys[j].code === code) {
                    return district[i].citys[j].name;
                }
            }
        }
    }

    return {
        list: district,
        provinces: provinces,
        getProvinceOfCity: getProvinceOfCity,
        getCitysOfProvince: getCitysOfProvince,
        getNameByCode: getNameByCode
    }

});
