﻿<!--
    * report v1.0
    *
    * name: xiaojia
    * date: 2013/10/12
-->

<!--main-->



<script id="swallow-plugin-upload-template-main-index" type="text/html">
    <style>
        .bug-manage-upload-process {
            height: 100%;
            width: 0%;
            background: #b0a758;
            position: absolute;
            left: 0;
            padding: 8px 0;
            border-radius: 4px;
            top: 0;
            text-align: center;
            background: rgba(19, 122, 58, 0.5);
            color: #fff;
            line-height: 100%;
        }

        .bug-manage-upload-process.success {

            background: rgba(19, 122, 58, 0.9);
            width: 100%;
        }

        .bug-manage-upload-process.error {

            color: red;
            background: rgba(246, 216, 212, 0.9);
            width: 100%;

        }

        .upload-success .image-wraper {
            width: 110px;
            height: 100px;
            position: relative;
            margin: 10px 0 0 15px;
            display: inline-block
        }

        .upload-success .upload-image {
            width: 110px;
            height: 100px;
        }

        .upload-success .close-icon {
            position: absolute;
            top: 0;
            right: 0;
            color: white;
            font-weight: bold;
            width: 20px;
            height: 20px;
            background: #000;
            border-radius: 10px;
            opacity: 0.7;
            text-align: center;
            cursor: pointer
        }
    </style>
    <?
        if (typeof config.check === 'string') {
            check = config.check;
    }
        ?>
    <div class="clearfix">
        <div class="col-sm-9" style="padding-left: 0;">
            <div style="position: relative">
                <input type="hidden" name="<?=config.dataIndex ?>" class="form-control <?=check ?>"
                    data-item="bug-manage-value" />
                <input type="text" readonly placeholder="<?=config.placeholder ?>" class="form-control" />
                <div class="bug-manage-upload-process" data-item="bug-manage-process"></div>
            </div>
        </div>
        <button type="button" class="btn btn-<?=config.class || 'default'; ?> col-sm-3" style="position: relative;">
            <span><?=config.button || '选择文件' ?></span>
            <?if(config.isSingle){?>
            <input data-item="<?=config.item || config.dataIndex ?>-file"
                onchange="$(this).parent().parent().find(':text:eq(0)').val(this.files[0].name);"
                style="width: 100%; height: 34px; position: absolute; top: 0; left: 0; opacity: 0;" <? if
                (config.multiple) { ?>multiple
            <? } ?> accept="<?=config.accept ?>" type="file"
            <? if (config.readonly) { ?>readonly
            <? } ?>
            <? if (config.disabled) { ?>disabled
            <? } ?> class="form-control" />
            <?}else{?>
            <input multiple="multiple" data-item="<?=config.item || config.dataIndex ?>-file"
                onchange="$(this).parent().parent().find(':text:eq(0)').val(this.files[0].name);"
                style="width: 100%; height: 34px; position: absolute; top: 0; left: 0; opacity: 0;" <? if
                (config.multiple) { ?>multiple
            <? } ?> accept="<?=config.accept ?>" type="file"
            <? if (config.readonly) { ?>readonly
            <? } ?>
            <? if (config.disabled) { ?>disabled
            <? } ?> class="form-control" />
            <?}?>
        </button>
    </div>
    <div class="form-group clearfix upload-success" data-item="upload-successed">

    </div>
</script>