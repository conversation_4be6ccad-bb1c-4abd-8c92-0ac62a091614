﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>ji<PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'swallow://api/admin/ab-test/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/ab-test/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'swallow://api/admin/ab-test/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/ab-test/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/ab-test/view.htm',
            type: 'get'
        }
    }

    var filter = {
        load: {
            url: 'swallow://api/admin/ab-test/conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'swallow://api/admin/ab-test/conditions/update.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        filter: filter
    }

});