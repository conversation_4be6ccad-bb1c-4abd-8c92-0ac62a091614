﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON>a
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'swallow://api/admin/icon-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/icon-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'swallow://api/admin/icon-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/icon-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/icon-config/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});