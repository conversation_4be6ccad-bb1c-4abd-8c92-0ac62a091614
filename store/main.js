/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
*/

'use strict';

define(function () {

    var header = {
        load: {
            type: 'data',
            data: [{
                name: '',
                item: 'header-nav',
                aside: 'swallow!main/aside'
            }
            ]
        }
    }

    var aside = {
        load: {
            type: 'data',
            data: [
                {
                    name: '驾考配置',
                    nodes: [
                        {
                            id: 'ab-test',
                            name: 'ab-test',
                            app: 'swallow!app/ab-test/index/list',
                            item: 'aside-nav'
                        },
                        {
                            id: 'remote-config',
                            name: '远程配置',
                            app: 'swallow!app/remote-config/index/list',
                            item: 'aside-nav'
                        }, {
                            id: 'icon-config',
                            name: 'icon-config',
                            app: 'swallow!app/icon-config/index/list',
                            item: 'aside-nav'
                        }, {
                            id: 'skin-config',
                            name: '皮肤远程配置',
                            item: 'aside-nav',
                            app: 'swallow!app/skin-config/index/list',
                        }, {
                            id: 'emoji',
                            name: 'emoji',
                            app: 'swallow!app/emoji/index/list',
                            item: 'aside-nav'
                        },
                        {
                            id: 'photo-scene-config',
                            name: '拍照识别场景配置',
                            app: 'swallow!app/photo-scene-config/index/list',
                            item: 'aside-nav'
                        },
                        {
                            id: 'video-patch',
                            name: '视频贴片',
                            app: 'swallow!app/video-patch/index/list',
                            item: 'aside-nav'
                        }
                    ]
                }
            ]
        }
    }


    return {
        header: header,
        aside: aside,

    }

});