﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

let whiteList = ['updateTime', 'updateUserId', 'updateUserName','createTime', 'createUserId', 'createUserName', 'id', 'skinId']
function getDataFromObj(obj, whiteList) {
    let newObj = {}
    for(let key in obj) {
        if(whiteList.indexOf(key) > -1){
            newObj[key] = obj[key]
        }
    }
    return newObj;
}
define(function() {
    var list = {
        load: {
            url: 'swallow://api/admin/skin-content/list.htm',
            type: 'get',
            format: function (data, config) {
                if(config.params.shut) {
                    if(config.params.dataType == 'array') {
                        let _data = []
                        let index = 0
                        data.forEach(item => {
                            let comData = getDataFromObj(item, whiteList)
                            item = JSON.parse(item[config.params.shut] || '[{}]')
                            item[config.params.shut].forEach(item2 => {
                                item2 = $.extend(true, {index: index++}, item2, comData)
                                _data.push(item2)
                            })
                        })
                        data = _data
                    }else {
                        data = data.map(item => {
                            let comData = getDataFromObj(item, whiteList)
                            item = JSON.parse(item[config.params.shut])
                            item = $.extend(true, {}, item[config.params.shut], comData)
                            return item
                        })
                    }
                }
                return data
            }
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/skin-content/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'swallow://api/admin/skin-content/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/skin-content/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'index',
        load: {
            url: 'swallow://api/admin/skin-content/view.htm',
            type: 'get',
            format: function (data, config) {
                if(config.params.shut) {
                    if(config.params.dataType == 'array') {
                        let comData = getDataFromObj(data, whiteList)
                        let index = config.params.index
                        comData.index = index
                        data = JSON.parse(data[config.params.shut])
                        data = $.extend(true, {}, data[config.params.shut][index], comData)
                    }else{
                        let comData = getDataFromObj(data, whiteList)
                        data = JSON.parse(data[config.params.shut])
                        data = $.extend(true, {}, data[config.params.shut], comData)
                    }
                }
                return data
            }
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});