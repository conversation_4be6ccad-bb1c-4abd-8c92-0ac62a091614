﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'swallow://api/admin/video-patch/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/video-patch/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'swallow://api/admin/video-patch/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/video-patch/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/video-patch/view.htm',
            type: 'get'
        }
    }

    var typeList = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/video-patch/patch-type-list.htm',
            type: 'get'
        }
    }

    var videoTypeList = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/video-patch/patch-video-type-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        typeList,
        videoTypeList
    }

});