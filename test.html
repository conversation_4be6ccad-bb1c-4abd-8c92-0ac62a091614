<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>加载中...</title>
</head>

<body>

    <script>
        // 所有配置的路径最后都不以 "/" 结尾，程序会自动增加
        ! function () {

            // 加载成功之后执行的方法
            var success = function () {

            }

            var config = {

                // 产品标题
                title: 'swallow',
                name: 'swallow',

                // true 则加载本地文件，默认从当前目录开始， false则从framework.root加载
                debug: false,

                // ajax.js判断host，如果找到匹配则替换，例：
                // host: {
                //     local: 'http://w3c.org'
                // }
                // Ajax.require('local://m.json') > Ajax.require('http://w3c.org/m.json');
                // PS：此配置从v1.1版本开始生效
                host: {
                    // API地址
                    "swallow": 'https://swallow.ttt.mucang.cn',

                    // 用户信息登录的URL，一般和API地址相同
                    local: 'https://swallow.ttt.mucang.cn'
                    //                    // 用户信息登录的URL，一般和API地址相同
                    //                    local: 'http://localhost:8180'
                },

                // 前端文件根目录，最后没有 "/"
                root: {
                    "swallow": window.location.href.replace(/\/[^/]*$/ig, '')
                },

                // 框架远程文件配置
                framework: {

                    // 远程文件路径，PS：不带版本目录
                    root: 'https://static.kakamobi.cn/simple-framework',

                    // 版本目录，{version: 1} 代表 "/v1/"
                    version: '1.1'

                },

                // 入口文件需要的JS和CSS
                file: {
                    js: [],
                    css: []
                }

            }

            window.mapToArray = function (obj) {
                return Object.keys(obj).map(key => ({ key, value: obj[key] }))

            }

            var script = document.createElement('script');
            script.src = config.framework.root + '/v' + config.framework.version + '/resources/js/require.min.js';
            document.body.appendChild(script);

            script.onload = function () {
                require([config.framework.root + '/v' + config.framework.version + '/app/main.js'], function (app) {
                    app.init(config, success);
                });
            }

        }();
    </script>

</body>

</html>